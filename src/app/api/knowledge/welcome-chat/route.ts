import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { nanoid } from 'nanoid'
import { v4 as uuidv4 } from 'uuid'
import { audioRedis } from '@/utils/redis'
import { telegramAudio } from '@/utils/telegram'
import { convertToMP4A, validateAudioBuffer, getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'

// No interface needed - using the old updateData pattern

// GET - Fetch welcome chat data for the client
export async function GET() {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    const chatId = `${clientId}-1`

    // Fetch welcome chat data with specific client_id and chat_id
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `
          SELECT chat_id, answer_p, audio_url, audio_file_path, photo_url, photo_id
          FROM welcome_chat 
          WHERE client_id = $1 AND chat_id = $2
          ORDER BY created_at DESC
        `,
        params: [clientId, chatId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    let rawRecord = webhookData.body
    
    // Handle double array wrapper for welcome chat response
    if (Array.isArray(rawRecord) && rawRecord.length > 0) {
      rawRecord = rawRecord[0]
    }
    
    // Handle case where no record exists
    if (!rawRecord) {
      return NextResponse.json({ 
        success: true,
        body: [],
        error_msg: null
      })
    }
    
    // Process the single record
    const record = rawRecord as Record<string, unknown>
    
    // Check if audio exists (has audio_url)
    const hasAudio = record.audio_url && typeof record.audio_url === 'string' && record.audio_url.trim() !== ''
    
    // Check if photo exists (has photo_url and photo_id)
    const hasPhoto = record.photo_url && record.photo_id && 
                    Array.isArray(record.photo_url) && record.photo_url.length > 0 && 
                    typeof record.photo_id === 'string' && record.photo_id.trim() !== ''
    
    const processedRecord = {
      chat_id: record.chat_id,
      answer_p: hasAudio ? '' : (record.answer_p || ''), // Clear text if audio mode
      audio_url: record.audio_url || null,
      audio_file_path: record.audio_file_path || null,
      audioCode: hasAudio ? 'existing_audio' : '', // Set to empty if no audio
      photo_url: hasPhoto ? record.photo_url : [], // Set to empty array if no photo
      photo_id: hasPhoto ? record.photo_id : '' // Set to empty string if no photo
    }
    
    // Return as array to match frontend expectations
    const processedWelcomeData = [processedRecord]

    return NextResponse.json({ 
      success: true,
      body: processedWelcomeData,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat GET API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update welcome chat data
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Parse request body with old structure
    const body = await request.json()
    const { chat_id, updateData, sector, lang } = body

    if (!chat_id || !updateData || !sector || !lang) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required fields: chat_id, updateData, sector, lang'
      }, { status: 400 })
    }

    // Create Supabase client (still needed for audio file operations)
    const cookieStore = cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
          set(_name: string, _value: string, _options: unknown) {
            // Not needed for this context
          },
          remove(_name: string, _options: unknown) {
            // Not needed for this context
          },
        },
      }
    )

    // Handle audio processing based on explicit flags (like knowledge base)
    if (updateData.hasAudioCodeChanged && updateData.isAudioAnswer && updateData.audioCode) {

      // Get audio data from Redis first
      const audioData = await audioRedis.getAudioData(updateData.audioCode)
      
      if (!audioData) {
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Audio code ${updateData.audioCode} not found or expired`
        }, { status: 400 })
      }
      

      try {
        // Download audio from Telegram
        const { buffer, mimeType } = await telegramAudio.downloadAudioFile(audioData.file_id)
        
        // Validate audio buffer
        const validation = validateAudioBuffer(buffer, mimeType)
        if (!validation.valid) {
          throw new Error(`Invalid audio data: ${validation.error}`)
        }
        
        // Convert audio to MP4A AAC format
        let finalBuffer: Buffer
        let finalMimeType: string
        
        try {
          const conversionResult = await convertToMP4A(buffer, mimeType)
          finalBuffer = conversionResult.buffer
          finalMimeType = conversionResult.mimeType
        } catch (error) {
          const conversionError = error as Error
          console.error('Audio conversion failed:', conversionError)
          throw new Error(`Audio conversion failed: ${conversionError.message}. Please ensure FFmpeg is installed and try again.`)
        }
        
        // Generate audio ID from the audio code with unique suffix
        const audioCode = updateData.audioCode // Use the audio code as the base
        const uniqueId = uuidv4() // Proper UUID for uniqueness
        const audioId = `${audioCode}-${uniqueId}`
        const fileExtension = getM4AExtension()
        
        // Upload to R2 using the new audio API
        
        // Create form data for the R2 upload
        const formData = new FormData()
        const audioFile = new File([finalBuffer], `${audioId}.${fileExtension}`, {
          type: finalMimeType
        })
        formData.append('files', audioFile)
        formData.append('audioId', audioId)
        
        // Upload to R2 via our new API
        const uploadResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'POST',
          body: formData,
          headers: {
            // Pass authentication headers
            'Cookie': request.headers.get('Cookie') || ''
          }
        })
        
        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json()
          throw new Error(`R2 upload failed: ${errorData.error || 'Unknown error'}`)
        }
        
        const uploadResult = await uploadResponse.json()
        if (!uploadResult.success || !uploadResult.audioUrls || uploadResult.audioUrls.length === 0) {
          throw new Error('R2 upload succeeded but no URLs returned')
        }
        
        // Update the updateData with audio information
        updateData.audio_url = uploadResult.audioUrls[0]
        updateData.audio_file_path = uploadResult.filePaths[0]
        // Clear answer_p for audio answers
        updateData.answer_p = ""

        // Fire off old audio deletion asynchronously (don't wait for it)
        if (updateData.originalAudioFilePath) {
          fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('Cookie') || ''
            },
            body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
          }).catch(cleanupError => {
            console.error('Background cleanup failed for old intro audio file:', updateData.originalAudioFilePath, cleanupError)
            // Silently fail - this won't affect the user experience
          })
        }

      } catch (audioError: unknown) {
        console.error('Error processing audio:', audioError)
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Failed to process audio: ${audioError instanceof Error ? audioError.message : 'Unknown error'}`
        }, { status: 500 })
      }
    } else if (updateData.originalAudioFilePath && updateData.isAudioAnswer === false) {
      // Handle cleanup when switching from audio to text mode
      try {
        // Fire off old audio deletion asynchronously (don't wait for it)
        fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': request.headers.get('Cookie') || ''
          },
          body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
        }).catch(cleanupError => {
          console.error('Background cleanup failed when switching to text mode:', updateData.originalAudioFilePath, cleanupError)
          // Silently fail - this won't affect the user experience
        })

        // Clear audio-related fields
        updateData.audio_url = null
        updateData.audio_file_path = null
      } catch (cleanupError) {
        console.error('Error during audio cleanup when switching to text mode:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Remove fields that don't exist in welcome_chat table before database update
    const { isAudioAnswer, audioCode, hasAudioCodeChanged, originalAudioFilePath, is_audio, onlyPhoto, ...dbUpdateData } = updateData

    // Prepare update fields and values for PostgreSQL
    const updateFields: string[] = []
    const updateValues: unknown[] = []
    let valueIndex = 3

    // Build dynamic update query based on changed fields
    Object.entries(dbUpdateData).forEach(([key, value]) => {
      updateFields.push(`${key} = $${valueIndex}`)
      updateValues.push(value)
      valueIndex++
    })

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No fields to update'
      }, { status: 400 })
    }

    // Extract helper fields for webhook processing
    const helperAnswer = updateData.answer_p || null;
    const helperAudioUrl = updateData.audio_url || null;

    // Update welcome chat using PostgreSQL via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'UPDATE',
        mode: 'welcome',
        sector: sector || null,
        lang: lang || null,
        is_audio: updateData.is_audio || false,
        onlyPhoto: updateData.onlyPhoto || false,
        // Helper fields for easier webhook processing
        clientId: clientId,
        chat_id: chat_id,
        answer_p: helperAnswer,
        audio_url: helperAudioUrl,
        sql: `
          UPDATE welcome_chat 
          SET ${updateFields.join(', ')}
          WHERE client_id = $1 AND chat_id = $2
        `,
        params: [clientId, chat_id, ...updateValues]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // No cache update needed - welcome data is fetched fresh each time
    // Frontend handles optimistic UI updates with local state

    // Return the new audio URL if audio was processed
    const responseBody = updateData.hasAudioCodeChanged && updateData.audio_url ? {
      audio_url: updateData.audio_url,
      audio_file_path: updateData.audio_file_path
    } : null

    return NextResponse.json({
      success: true,
      body: responseBody,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}