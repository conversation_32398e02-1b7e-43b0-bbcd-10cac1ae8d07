import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/utils/auth'
import { generateWebhookToken } from '@/utils/jwt'
import { v4 as uuidv4 } from 'uuid'
import { getM4AExtension } from '@/utils/audioConversion'
import { serverCache } from '@/lib/cache'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'

// R2 Configuration
const BUCKET_NAME = process.env.R2_BUCKET_NAME!
const PUBLIC_URL = process.env.R2_PUBLIC_URL!

const r2Client = new S3Client({
  region: 'auto',
  endpoint: process.env.R2_ENDPOINT!,
  credentials: {
    accessKeyId: process.env.R2_ACCESS_KEY_ID!,
    secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
  },
})

// Helper function to get content type
function getContentType(fileName: string): string {
  const extension = fileName.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'm4a':
      return 'audio/mp4'
    case 'mp3':
      return 'audio/mpeg'
    case 'wav':
      return 'audio/wav'
    case 'ogg':
      return 'audio/ogg'
    default:
      return 'audio/mp4'
  }
}

// No interface needed - using the old updateData pattern

// GET - Fetch welcome chat data for the client
export async function GET() {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    const chatId = `${clientId}-1`

    // Fetch welcome chat data with specific client_id and chat_id
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'SELECT',
        sql: `
          SELECT chat_id, answer_p, audio_url, audio_file_path, photo_url, photo_id
          FROM welcome_chat 
          WHERE client_id = $1 AND chat_id = $2
          ORDER BY created_at DESC
        `,
        params: [clientId, chatId]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    let rawRecord = webhookData.body
    
    // Handle double array wrapper for welcome chat response
    if (Array.isArray(rawRecord) && rawRecord.length > 0) {
      rawRecord = rawRecord[0]
    }
    
    // Handle case where no record exists
    if (!rawRecord) {
      return NextResponse.json({ 
        success: true,
        body: [],
        error_msg: null
      })
    }
    
    // Process the single record
    const record = rawRecord as Record<string, unknown>
    
    // Check if audio exists (has audio_url)
    const hasAudio = record.audio_url && typeof record.audio_url === 'string' && record.audio_url.trim() !== ''
    
    // Check if photo exists (has photo_url and photo_id)
    const hasPhoto = record.photo_url && record.photo_id && 
                    Array.isArray(record.photo_url) && record.photo_url.length > 0 && 
                    typeof record.photo_id === 'string' && record.photo_id.trim() !== ''
    
    const processedRecord = {
      chat_id: record.chat_id,
      answer_p: hasAudio ? '' : (record.answer_p || ''), // Clear text if audio mode
      audio_url: record.audio_url || null,
      audio_file_path: record.audio_file_path || null,
      audioCode: hasAudio ? 'existing_audio' : '', // Set to empty if no audio
      photo_url: hasPhoto ? record.photo_url : [], // Set to empty array if no photo
      photo_id: hasPhoto ? record.photo_id : '' // Set to empty string if no photo
    }
    
    // Return as array to match frontend expectations
    const processedWelcomeData = [processedRecord]

    return NextResponse.json({ 
      success: true,
      body: processedWelcomeData,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat GET API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}

// PUT - Update welcome chat data
export async function PUT(request: NextRequest) {
  try {
    // Verify authentication
    const { authenticated, authId, clientId } = await verifyAuth()
    if (!authenticated || !authId) {
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Unauthorized'
      }, { status: 401 })
    }

    // Validate clientId exists
    if (!clientId) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Client ID not found in authentication'
      }, { status: 400 })
    }

    // Get the secure webhook URL from environment
    const webhookUrl = process.env.CHHLAT_DB_WEBHOOK_URL
    
    if (!webhookUrl) {
      console.warn('Secure webhook URL not configured')
      return NextResponse.json({ 
        success: false,
        body: null,
        error_msg: 'Database webhook not configured'
      }, { status: 500 })
    }

    // Generate JWT token for webhook authentication
    const jwtToken = generateWebhookToken()

    // Parse request body - handle both FormData (with audio) and JSON
    let chat_id: string, updateData: any, sector: string, lang: string;
    let audioBlob: Blob | null = null;

    const contentType = request.headers.get('content-type') || '';

    if (contentType.includes('multipart/form-data')) {
      // Handle FormData (with audio blob)
      const formData = await request.formData();
      chat_id = formData.get('chat_id') as string;
      updateData = JSON.parse(formData.get('updateData') as string);
      sector = formData.get('sector') as string;
      lang = formData.get('lang') as string;
      audioBlob = formData.get('audioBlob') as Blob | null;
    } else {
      // Handle JSON (no audio)
      const body = await request.json();
      ({ chat_id, updateData, sector, lang } = body);
    }

    if (!chat_id || !updateData || !sector || !lang) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'Missing required fields: chat_id, updateData, sector, lang'
      }, { status: 400 })
    }

    // Handle audio processing with blob upload (like lists API)
    if (audioBlob && updateData.hasAudioCodeChanged && updateData.isAudioAnswer) {

      try {
        // Convert blob to buffer for R2 upload
        const processedAudioBlob = audioBlob;
        const audioBuffer = Buffer.from(await processedAudioBlob.arrayBuffer());

        // Generate audio ID and file path with proper UUID
        const audioCode = updateData.audioCode || 'intro'
        const uniqueId = uuidv4()
        const audioId = `${audioCode}-${uniqueId}`
        const fileExtension = getM4AExtension()
        const fileName = `${audioId}.${fileExtension}`
        const filePath = `audios/${authId}/${fileName}`

        // Direct R2 upload - optimized to not use /api/file/audios
        const contentType = getContentType(fileName)

        const putCommand = new PutObjectCommand({
          Bucket: BUCKET_NAME,
          Key: filePath,
          Body: new Uint8Array(audioBuffer),
          ContentType: contentType,
          CacheControl: '3600',
          ContentDisposition: `inline; filename="${fileName}"`,
        })

        await r2Client.send(putCommand)

        // Generate public URL
        const publicUrl = `${PUBLIC_URL}/${filePath}`

        // Update the updateData with audio information
        updateData.audio_url = publicUrl
        updateData.audio_file_path = filePath

        // Fire off old audio deletion asynchronously (don't wait for it)
        if (updateData.originalAudioFilePath) {
          fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'Cookie': request.headers.get('Cookie') || ''
            },
            body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
          }).catch(cleanupError => {
            console.error('Background cleanup failed for old intro audio file:', updateData.originalAudioFilePath, cleanupError)
            // Silently fail - this won't affect the user experience
          })
        }

      } catch (audioError: unknown) {
        console.error('Error processing audio:', audioError)
        return NextResponse.json({
          success: false,
          body: null,
          error_msg: `Failed to process audio: ${audioError instanceof Error ? audioError.message : 'Unknown error'}`
        }, { status: 500 })
      }
    } else if (updateData.originalAudioFilePath && updateData.isAudioAnswer === false) {
      // Handle cleanup when switching from audio to text mode
      try {
        // Fire off old audio deletion asynchronously (don't wait for it)
        fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/api/file/audios`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': request.headers.get('Cookie') || ''
          },
          body: JSON.stringify({ filePaths: [updateData.originalAudioFilePath] })
        }).catch(cleanupError => {
          console.error('Background cleanup failed when switching to text mode:', updateData.originalAudioFilePath, cleanupError)
          // Silently fail - this won't affect the user experience
        })

        // Clear audio-related fields
        updateData.audio_url = null
        updateData.audio_file_path = null
      } catch (cleanupError) {
        console.error('Error during audio cleanup when switching to text mode:', cleanupError)
        // Don't fail the operation if cleanup fails
      }
    }

    // Remove fields that don't exist in welcome_chat table before database update
    const { isAudioAnswer, audioCode, hasAudioCodeChanged, originalAudioFilePath, is_audio, onlyPhoto, ...dbUpdateData } = updateData

    // Prepare update fields and values for PostgreSQL
    const updateFields: string[] = []
    const updateValues: unknown[] = []
    let valueIndex = 3

    // Build dynamic update query based on changed fields
    Object.entries(dbUpdateData).forEach(([key, value]) => {
      updateFields.push(`${key} = $${valueIndex}`)
      updateValues.push(value)
      valueIndex++
    })

    if (updateFields.length === 0) {
      return NextResponse.json({
        success: false,
        body: null,
        error_msg: 'No fields to update'
      }, { status: 400 })
    }

    // Extract helper fields for webhook processing
    const helperAnswer = updateData.answer_p || null;
    const helperAudioUrl = updateData.audio_url || null;

    // Update welcome chat using PostgreSQL via webhook
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${jwtToken}`
      },
      body: JSON.stringify({
        operation: 'UPDATE',
        mode: 'welcome',
        sector: sector || null,
        lang: lang || null,
        is_audio: updateData.is_audio || false,
        onlyPhoto: updateData.onlyPhoto || false,
        // Helper fields for easier webhook processing
        clientId: clientId,
        chat_id: chat_id,
        answer_p: helperAnswer,
        audio_url: helperAudioUrl,
        sql: `
          UPDATE welcome_chat 
          SET ${updateFields.join(', ')}
          WHERE client_id = $1 AND chat_id = $2
        `,
        params: [clientId, chat_id, ...updateValues]
      })
    })

    if (!response.ok) {
      throw new Error(`Webhook request failed: ${response.status} ${response.statusText}`)
    }

    let webhookData = await response.json()
    
    // Handle array response format from N8N webhook
    if (Array.isArray(webhookData) && webhookData.length > 0) {
      webhookData = webhookData[0]
    }
    
    if (!webhookData.success) {
      throw new Error(webhookData.error_msg || 'Database operation failed')
    }

    // No cache update needed - welcome data is fetched fresh each time
    // Frontend handles optimistic UI updates with local state

    // Return the new audio URL if audio was processed
    const responseBody = updateData.hasAudioCodeChanged && updateData.audio_url ? {
      audio_url: updateData.audio_url,
      audio_file_path: updateData.audio_file_path
    } : null

    return NextResponse.json({
      success: true,
      body: responseBody,
      error_msg: null
    })

  } catch (error: unknown) {
    console.error('Error in welcome chat PUT API:', error)
    return NextResponse.json(
      { 
        success: false,
        body: null,
        error_msg: error instanceof Error ? error.message : 'Internal server error'
      },
      { status: 500 }
    )
  }
}